'use client';

import * as React from 'react';
import PropTypes from 'prop-types';
import composeClasses from '@mui/utils/composeClasses';
import { getAxisHighlightUtilityClass } from "./chartsAxisHighlightClasses.js";
import ChartsYHighlight from "./ChartsYAxisHighlight.js";
import ChartsXHighlight from "./ChartsXAxisHighlight.js";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
const useUtilityClasses = () => {
  const slots = {
    root: ['root']
  };
  return composeClasses(slots, getAxisHighlightUtilityClass);
};

/**
 * Demos:
 *
 * - [Custom components](https://mui.com/x/react-charts/components/)
 *
 * API:
 *
 * - [ChartsAxisHighlight API](https://mui.com/x/api/charts/charts-axis-highlight/)
 */
function ChartsAxisHighlight(props) {
  const {
    x: xAxisHighlight,
    y: yAxisHighlight
  } = props;
  const classes = useUtilityClasses();
  return /*#__PURE__*/_jsxs(React.Fragment, {
    children: [xAxisHighlight && xAxisHighlight !== 'none' && /*#__PURE__*/_jsx(ChartsXHighlight, {
      type: xAxisHighlight,
      classes: classes
    }), yAxisHighlight && yAxisHighlight !== 'none' && /*#__PURE__*/_jsx(ChartsYHighlight, {
      type: yAxisHighlight,
      classes: classes
    })]
  });
}
process.env.NODE_ENV !== "production" ? ChartsAxisHighlight.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  x: PropTypes.oneOf(['band', 'line', 'none']),
  y: PropTypes.oneOf(['band', 'line', 'none'])
} : void 0;
export { ChartsAxisHighlight };