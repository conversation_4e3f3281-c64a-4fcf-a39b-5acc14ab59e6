export type ChartsTextBaseline = 'hanging' | 'central' | 'auto' | 'text-after-edge' | 'text-before-edge';
export interface ChartsTextStyle extends React.CSSProperties {
  angle?: number;
  /**
   * The text baseline
   * @default 'central'
   */
  dominantBaseline?: ChartsTextBaseline;
}
export interface GetWordsByLinesParams {
  /**
   * Text displayed.
   */
  text: string;
  /**
   * Style applied to text elements.
   */
  style?: ChartsTextStyle;
  /**
   * If `true`, the line width is computed.
   * @default false
   */
  needsComputation?: boolean;
}
export declare function getWordsByLines({
  style,
  needsComputation,
  text
}: GetWordsByLinesParams): {
  width: number;
  height: number;
  text: string;
}[];