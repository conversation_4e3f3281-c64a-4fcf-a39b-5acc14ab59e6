import type { MuiBaseEvent, MuiEvent } from '@mui/x-internals/types';
import type { GridCallbackDetails } from "../api/gridCallbackDetails.js";
import type { GridEventLookup, GridEvents } from "./gridEventLookup.js";
export type GridEventListener<E extends GridEvents> = (params: GridEventLookup[E] extends {
  params: any;
} ? GridEventLookup[E]['params'] : undefined, event: GridEventLookup[E] extends {
  event: MuiBaseEvent;
} ? MuiEvent<GridEventLookup[E]['event']> : MuiEvent<{}>, details: GridCallbackDetails) => void;