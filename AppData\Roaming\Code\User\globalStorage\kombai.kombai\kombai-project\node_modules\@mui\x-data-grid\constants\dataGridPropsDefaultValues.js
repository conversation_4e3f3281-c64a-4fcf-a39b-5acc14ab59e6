"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DATA_GRID_PROPS_DEFAULT_VALUES = void 0;
var _gridEditRowModel = require("../models/gridEditRowModel");
/**
 * The default values of `DataGridPropsWithDefaultValues` to inject in the props of DataGrid.
 */
const DATA_GRID_PROPS_DEFAULT_VALUES = exports.DATA_GRID_PROPS_DEFAULT_VALUES = {
  autoHeight: false,
  autoPageSize: false,
  autosizeOnMount: false,
  checkboxSelection: false,
  checkboxSelectionVisibleOnly: false,
  clipboardCopyCellDelimiter: '\t',
  columnBufferPx: 150,
  columnFilterDebounceMs: 150,
  columnHeaderHeight: 56,
  disableAutosize: false,
  disableColumnFilter: false,
  disableColumnMenu: false,
  disableColumnReorder: false,
  disableColumnResize: false,
  disableColumnSelector: false,
  disableColumnSorting: false,
  disableDensitySelector: false,
  disableEval: false,
  disableMultipleColumnsFiltering: false,
  disableMultipleColumnsSorting: false,
  disableMultipleRowSelection: false,
  disableRowSelectionOnClick: false,
  disableVirtualization: false,
  editMode: _gridEditRowModel.GridEditModes.Cell,
  filterDebounceMs: 150,
  filterMode: 'client',
  hideFooter: false,
  hideFooterPagination: false,
  hideFooterRowCount: false,
  hideFooterSelectedRowCount: false,
  ignoreDiacritics: false,
  ignoreValueFormatterDuringExport: false,
  keepColumnPositionIfDraggedOutside: false,
  keepNonExistentRowsSelected: false,
  loading: false,
  logger: console,
  logLevel: process.env.NODE_ENV === 'production' ? 'error' : 'warn',
  pageSizeOptions: [25, 50, 100],
  pagination: false,
  paginationMode: 'client',
  resizeThrottleMs: 60,
  rowBufferPx: 150,
  rowHeight: 52,
  rows: [],
  rowSelection: true,
  rowSpacingType: 'margin',
  rowSpanning: false,
  showCellVerticalBorder: false,
  showColumnVerticalBorder: false,
  showToolbar: false,
  sortingMode: 'client',
  sortingOrder: ['asc', 'desc', null],
  throttleRowsMs: 0,
  virtualizeColumnsWithAutoRowHeight: false
};