import * as React from 'react';
import type { GridColumnHeaderParams } from "../../models/params/gridColumnHeaderParams.js";
declare const GridHeaderCheckbox: React.ForwardRefExoticComponent<GridColumnHeaderParams<import("@mui/x-data-grid").GridValidRowModel, any, any>> | React.ForwardRefExoticComponent<GridColumnHeaderParams<import("@mui/x-data-grid").GridValidRowModel, any, any> & React.RefAttributes<HTMLButtonElement>>;
export { GridHeaderCheckbox };