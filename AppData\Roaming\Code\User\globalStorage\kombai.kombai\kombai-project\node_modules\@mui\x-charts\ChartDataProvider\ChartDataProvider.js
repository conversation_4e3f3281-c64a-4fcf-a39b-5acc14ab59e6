"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ChartDataProvider = ChartDataProvider;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _material = require("../internals/material");
var _ChartsSlotsContext = require("../context/ChartsSlotsContext");
var _useChartDataProviderProps = require("./useChartDataProviderProps");
var _ChartProvider = require("../context/ChartProvider");
var _ChartsLocalizationProvider = require("../ChartsLocalizationProvider");
var _jsxRuntime = require("react/jsx-runtime");
/**
 * Orchestrates the data providers for the chart components and hooks.
 *
 * Use this component if you have custom HTML components that need to access the chart data.
 *
 * Demos:
 *
 * - [Composition](https://mui.com/x/react-charts/composition/)
 *
 * API:
 *
 * - [ChartDataProvider API](https://mui.com/x/api/charts/chart-data-provider/)
 *
 * @example
 * ```jsx
 * <ChartDataProvider
 *   series={[{ label: "Label", type: "bar", data: [10, 20] }]}
 *   xAxis={[{ data: ["A", "B"], scaleType: "band", id: "x-axis" }]}
 * >
 *   <ChartsSurface>
 *      <BarPlot />
 *      <ChartsXAxis axisId="x-axis" />
 *   </ChartsSurface>
 *   {'Custom Legend Component'}
 * </ChartDataProvider>
 * ```
 */
function ChartDataProvider(props) {
  const {
    children,
    localeText,
    chartProviderProps,
    slots,
    slotProps
  } = (0, _useChartDataProviderProps.useChartDataProviderProps)(props);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ChartProvider.ChartProvider, (0, _extends2.default)({}, chartProviderProps, {
    children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_ChartsLocalizationProvider.ChartsLocalizationProvider, {
      localeText: localeText,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_ChartsSlotsContext.ChartsSlotsProvider, {
        slots: slots,
        slotProps: slotProps,
        defaultSlots: _material.defaultSlotsMaterial,
        children: children
      })
    })
  }));
}
process.env.NODE_ENV !== "production" ? ChartDataProvider.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: _propTypes.default.shape({
    current: _propTypes.default.any
  }),
  /**
   * Color palette used to colorize multiple series.
   * @default rainbowSurgePalette
   */
  colors: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.string), _propTypes.default.func]),
  /**
   * An array of objects that can be used to populate series and axes data using their `dataKey` property.
   */
  dataset: _propTypes.default.arrayOf(_propTypes.default.object),
  /**
   * Options to enable features planned for the next major.
   */
  experimentalFeatures: _propTypes.default.shape({
    preferStrictDomainInLineCharts: _propTypes.default.bool
  }),
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   */
  height: _propTypes.default.number,
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: _propTypes.default.string,
  /**
   * Localized text for chart components.
   */
  localeText: _propTypes.default.object,
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   *
   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   */
  margin: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.shape({
    bottom: _propTypes.default.number,
    left: _propTypes.default.number,
    right: _propTypes.default.number,
    top: _propTypes.default.number
  })]),
  /**
   * The array of series to display.
   * Each type of series has its own specificity.
   * Please refer to the appropriate docs page to learn more about it.
   */
  series: _propTypes.default.arrayOf(_propTypes.default.object),
  /**
   * If `true`, animations are skipped.
   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.
   */
  skipAnimation: _propTypes.default.bool,
  /**
   * The props for the slots.
   */
  slotProps: _propTypes.default.object,
  /**
   * Slots to customize charts' components.
   */
  slots: _propTypes.default.object,
  theme: _propTypes.default.oneOf(['dark', 'light']),
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   */
  width: _propTypes.default.number
} : void 0;