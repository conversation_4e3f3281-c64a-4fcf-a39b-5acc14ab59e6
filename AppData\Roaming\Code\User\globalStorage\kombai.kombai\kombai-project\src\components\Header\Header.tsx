import React, { useState } from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>,
  Typography,
  IconButton,
  Badge,
  Avatar,
  Menu,
  MenuItem,
  TextField,
  Stack,
  Box
} from '@mui/material';
import MenuOutlinedIcon from '@mui/icons-material/MenuOutlined';
import NotificationsOutlinedIcon from '@mui/icons-material/NotificationsOutlined';
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
import { UserProfile, NotificationItem } from '../../types/interfaces';

interface HeaderProps {
  currentUser: UserProfile;
  notifications: NotificationItem[];
  onMenuClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ currentUser, notifications, onMenuClick }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationAnchor, setNotificationAnchor] = useState<null | HTMLElement>(null);

  const handleProfileClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNotificationClick = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchor(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setNotificationAnchor(null);
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
      <Toolbar>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          onClick={onMenuClick}
          edge="start"
          sx={{ mr: 2 }}
        >
          <MenuOutlinedIcon />
        </IconButton>

        <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 0, mr: 4 }}>
          Mess Management System
        </Typography>

        <Box sx={{ flexGrow: 1, maxWidth: 400 }}>
          <TextField
            placeholder="Search meals..."
            variant="outlined"
            size="small"
            fullWidth
            InputProps={{
              startAdornment: <SearchOutlinedIcon sx={{ color: 'action.active', mr: 1 }} />,
              sx: {
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  border: '1px solid rgba(255, 255, 255, 0.5)',
                },
                color: 'white',
                '& input::placeholder': {
                  color: 'rgba(255, 255, 255, 0.7)',
                  opacity: 1,
                },
              },
            }}
          />
        </Box>

        <Stack direction="row" spacing={1} sx={{ ml: 2 }}>
          <IconButton color="inherit" onClick={handleNotificationClick}>
            <Badge badgeContent={unreadCount} color="error">
              <NotificationsOutlinedIcon />
            </Badge>
          </IconButton>

          <IconButton onClick={handleProfileClick} sx={{ p: 0 }}>
            <Avatar
              alt={currentUser.name}
              src={currentUser.avatar}
              sx={{ width: 32, height: 32 }}
            />
          </IconButton>
        </Stack>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
          onClick={handleClose}
        >
          <MenuItem>Profile</MenuItem>
          <MenuItem>Settings</MenuItem>
          <MenuItem>Logout</MenuItem>
        </Menu>

        <Menu
          anchorEl={notificationAnchor}
          open={Boolean(notificationAnchor)}
          onClose={handleClose}
          onClick={handleClose}
          PaperProps={{
            sx: { width: 300, maxHeight: 400 }
          }}
        >
          {notifications.length === 0 ? (
            <MenuItem>No new notifications</MenuItem>
          ) : (
            notifications.map((notification) => (
              <MenuItem key={notification.id} sx={{ whiteSpace: 'normal' }}>
                <Typography variant="body2">
                  {notification.message}
                </Typography>
              </MenuItem>
            ))
          )}
        </Menu>
      </Toolbar>
    </AppBar>
  );
};

export default Header;