"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createUseThemeProps = createUseThemeProps;
Object.defineProperty(exports, "styled", {
  enumerable: true,
  get: function () {
    return _styles.styled;
  }
});
var _styles = require("@mui/material/styles");
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function createUseThemeProps(name) {
  return _styles.useThemeProps;
}