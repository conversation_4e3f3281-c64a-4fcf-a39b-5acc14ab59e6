import React, { useState } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box, Toolbar } from '@mui/material';
import theme from './theme';
import Header from './components/Header/Header';
import Sidebar from './components/Sidebar/Sidebar';
import Dashboard from './components/Dashboard/Dashboard';
import MealSection from './components/MealSection/MealSection';
import OrderManagement from './components/OrderManagement/OrderManagement';
import WeeklyPlanner from './components/WeeklyPlanner/WeeklyPlanner';
import ImageGallery from './components/ImageGallery/ImageGallery';
import Reviews from './components/Reviews/Reviews';
import { mockRootProps, mockQuery } from './data/messMockData';
import { MealType, MealSize, DayOfWeek, OrderStatus } from './types/enums';
import { MealItem, ReviewItem } from './types/interfaces';

const App: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentView, setCurrentView] = useState('dashboard');

  const handleMenuClick = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleViewChange = (view: string) => {
    setCurrentView(view);
  };

  const handleAddToCart = (meal: MealItem, size: MealSize, quantity: number) => {
    console.log('Adding to cart:', { meal: meal.name, size, quantity });
    // In a real app, this would update the cart state
  };

  const handleViewDetails = (meal: MealItem) => {
    console.log('Viewing meal details:', meal.name);
    // In a real app, this would open a meal details modal
  };

  const handleCancelOrder = (orderId: string) => {
    console.log('Canceling order:', orderId);
    // In a real app, this would update the order status
  };

  const handleViewOrder = (order: any) => {
    console.log('Viewing order:', order.id);
  };

  const handleUpdateMenu = (day: DayOfWeek, mealType: MealType, mealIds: string[]) => {
    console.log('Updating menu:', { day, mealType, mealIds });
    // In a real app, this would update the weekly menu
  };

  const handleSubmitReview = (review: Omit<ReviewItem, 'id' | 'date'>) => {
    console.log('Submitting review:', review);
    // In a real app, this would submit the review to the API
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard stats={mockRootProps.dashboardStats} />;
      
      case 'breakfast':
        return (
          <MealSection
            title="Breakfast Menu"
            meals={mockQuery.meals}
            mealType={MealType.BREAKFAST}
            onAddToCart={handleAddToCart}
            onViewDetails={handleViewDetails}
          />
        );
      
      case 'lunch':
        return (
          <MealSection
            title="Lunch Menu"
            meals={mockQuery.meals}
            mealType={MealType.LUNCH}
            onAddToCart={handleAddToCart}
            onViewDetails={handleViewDetails}
          />
        );
      
      case 'dinner':
        return (
          <MealSection
            title="Dinner Menu"
            meals={mockQuery.meals}
            mealType={MealType.DINNER}
            onAddToCart={handleAddToCart}
            onViewDetails={handleViewDetails}
          />
        );
      
      case 'orders':
        return (
          <OrderManagement
            orders={mockQuery.orders}
            onCancelOrder={handleCancelOrder}
            onViewOrder={handleViewOrder}
          />
        );
      
      case 'weekly-menu':
        return (
          <WeeklyPlanner
            weeklyMenu={mockQuery.weeklyMenu}
            onUpdateMenu={handleUpdateMenu}
          />
        );
      
      case 'gallery':
        return <ImageGallery />;
      
      case 'reviews':
        return (
          <Reviews
            reviews={mockQuery.reviews}
            onSubmitReview={handleSubmitReview}
          />
        );
      
      case 'analytics':
        return (
          <Box>
            <h2>Analytics</h2>
            <p>Analytics dashboard coming soon...</p>
          </Box>
        );
      
      case 'settings':
        return (
          <Box>
            <h2>Settings</h2>
            <p>Settings panel coming soon...</p>
          </Box>
        );
      
      default:
        return <Dashboard stats={mockRootProps.dashboardStats} />;
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: 'flex' }}>
        <Header
          currentUser={mockRootProps.currentUser}
          notifications={mockRootProps.notifications}
          onMenuClick={handleMenuClick}
        />
        
        <Sidebar
          open={sidebarOpen}
          currentView={currentView}
          onViewChange={handleViewChange}
        />
        
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
            ml: sidebarOpen ? 0 : `-280px`,
            transition: 'margin-left 0.3s ease',
          }}
        >
          <Toolbar />
          {renderCurrentView()}
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default App;