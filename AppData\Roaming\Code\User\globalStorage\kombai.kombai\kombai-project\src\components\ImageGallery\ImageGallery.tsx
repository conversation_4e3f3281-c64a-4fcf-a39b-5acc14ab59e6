import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  IconButton,
  Dialog,
  DialogContent,
  DialogActions,
  <PERSON><PERSON>,
  Stack,
  Chip,
  TextField
} from '@mui/material';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import FavoriteIcon from '@mui/icons-material/Favorite';
import DownloadIcon from '@mui/icons-material/Download';
import CloseIcon from '@mui/icons-material/Close';

interface GalleryImage {
  id: string;
  src: string;
  title: string;
  category: string;
  photographer: string;
  photographerUrl: string;
}

interface ImageGalleryProps {
  images?: GalleryImage[];
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ images = [] }) => {
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Default images if none provided
  const defaultImages: GalleryImage[] = [
    {
      id: '1',
      src: 'https://images.unsplash.com/photo-1697880691504-a13456a05e3f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwzfHxwYW5lZXIlMjBjdXJyeSUyMGluZGlhbiUyMGZvb2QlMjBjcmVhbXklMjBzYXVjZXxlbnwwfDJ8fG9yYW5nZXwxNzU2ODM4MzEzfDA&ixlib=rb-4.1.0&q=85',
      title: 'Paneer Butter Masala',
      category: 'Indian Cuisine',
      photographer: 'Karyna Panchenko',
      photographerUrl: 'https://unsplash.com/@karyna_panchenko'
    },
    {
      id: '2',
      src: 'https://images.unsplash.com/photo-1630383249896-424e482df921?w=400',
      title: 'Masala Dosa',
      category: 'South Indian',
      photographer: 'Unsplash',
      photographerUrl: 'https://unsplash.com'
    },
    {
      id: '3',
      src: 'https://images.unsplash.com/photo-1697880691504-a13456a05e3f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw2fHxiaXJ5YW5pJTIwY2hpY2tlbiUyMHJpY2UlMjBhcm9tYXRpY3xlbnwwfDJ8fG9yYW5nZXwxNzU2ODM4MzEzfDA&ixlib=rb-4.1.0&q=85',
      title: 'Chicken Biryani',
      category: 'Indian Cuisine',
      photographer: 'Karyna Panchenko',
      photographerUrl: 'https://unsplash.com/@karyna_panchenko'
    },
    {
      id: '4',
      src: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
      title: 'Margherita Pizza',
      category: 'Italian',
      photographer: 'Unsplash',
      photographerUrl: 'https://unsplash.com'
    },
    {
      id: '5',
      src: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400',
      title: 'Pancakes',
      category: 'Breakfast',
      photographer: 'Unsplash',
      photographerUrl: 'https://unsplash.com'
    },
    {
      id: '6',
      src: 'https://images.unsplash.com/photo-**********-ba9599a7e63c?w=400',
      title: 'Fresh Salad',
      category: 'Healthy',
      photographer: 'Unsplash',
      photographerUrl: 'https://unsplash.com'
    }
  ];

  const galleryImages = images.length > 0 ? images : defaultImages;

  const categories = ['all', ...Array.from(new Set(galleryImages.map(img => img.category)))];

  const filteredImages = galleryImages
    .filter(img => selectedCategory === 'all' || img.category === selectedCategory)
    .filter(img => img.title.toLowerCase().includes(searchTerm.toLowerCase()));

  const handleImageClick = (image: GalleryImage) => {
    setSelectedImage(image);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedImage(null);
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
        Food Gallery
      </Typography>

      {/* Search and Filter Controls */}
      <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mb: 3 }}>
        <TextField
          placeholder="Search images..."
          variant="outlined"
          size="small"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ flex: 1 }}
        />
        
        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category.charAt(0).toUpperCase() + category.slice(1)}
              clickable
              color={selectedCategory === category ? 'primary' : 'default'}
              onClick={() => setSelectedCategory(category)}
            />
          ))}
        </Stack>
      </Stack>

      {/* Image Grid */}
      <ImageList variant="masonry" cols={3} gap={16}>
        {filteredImages.map((image) => (
          <ImageListItem key={image.id} sx={{ cursor: 'pointer' }}>
            <img
              src={image.src}
              alt={`${image.title} - ${image.photographer} on Unsplash`}
              loading="lazy"
              onClick={() => handleImageClick(image)}
              style={{
                borderRadius: 12,
                transition: 'transform 0.2s ease-in-out',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.02)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
              }}
            />
            <ImageListItemBar
              title={image.title}
              subtitle={image.category}
              actionIcon={
                <IconButton
                  sx={{ color: 'rgba(255, 255, 255, 0.54)' }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleImageClick(image);
                  }}
                >
                  <ZoomInIcon />
                </IconButton>
              }
            />
          </ImageListItem>
        ))}
      </ImageList>

      {filteredImages.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No images found
          </Typography>
        </Box>
      )}

      {/* Image Detail Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 3 }
        }}
      >
        <DialogContent sx={{ p: 0, position: 'relative' }}>
          {selectedImage && (
            <>
              <IconButton
                onClick={handleCloseDialog}
                sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  backgroundColor: 'rgba(0, 0, 0, 0.5)',
                  color: 'white',
                  zIndex: 1,
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  },
                }}
              >
                <CloseIcon />
              </IconButton>
              
              <img
                src={selectedImage.src}
                alt={`${selectedImage.title} - ${selectedImage.photographer} on Unsplash`}
                style={{
                  width: '100%',
                  height: 'auto',
                  maxHeight: '70vh',
                  objectFit: 'contain',
                }}
              />
              
              <Box sx={{ p: 3 }}>
                <Typography variant="h5" sx={{ mb: 1, fontWeight: 600 }}>
                  {selectedImage.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Category: {selectedImage.category}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Photo by{' '}
                  <a
                    href={selectedImage.photographerUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ color: 'inherit', textDecoration: 'underline' }}
                  >
                    {selectedImage.photographer}
                  </a>
                  {' '}on Unsplash
                </Typography>
              </Box>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <IconButton color="error">
            <FavoriteIcon />
          </IconButton>
          <IconButton color="primary">
            <DownloadIcon />
          </IconButton>
          <Button onClick={handleCloseDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ImageGallery;