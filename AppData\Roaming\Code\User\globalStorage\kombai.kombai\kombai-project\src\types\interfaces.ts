import { MealType, Meal<PERSON>ate<PERSON><PERSON>, Order<PERSON>tatus, MealSize, DayOfWeek } from './enums';

// Props types (data passed to components)
export interface DashboardProps {
  currentUser: UserProfile;
  notifications: NotificationItem[];
  dashboardStats: DashboardStats;
}

export interface UserProfile {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  role?: 'student' | 'admin' | 'staff';
  hostelBlock?: string;
  roomNumber?: string;
}

export interface NotificationItem {
  id: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  type?: 'order' | 'menu' | 'system';
}

export interface DashboardStats {
  todayOrders: number;
  weeklyOrders: number;
  totalMeals: number;
  avgRating: number;
}

// Store types (global state data)
export interface StoreTypes {
  user: UserProfile;
  cart: CartState;
  ui: UIState;
}

export interface CartState {
  items: CartItem[];
  total: number;
  isOpen: boolean;
}

export interface CartItem {
  id: string;
  name: string;
  type: MealType;
  size: MealSize;
  quantity: number;
  price: number;
  image?: string;
}

export interface UIState {
  sidebarOpen: boolean;
  currentView: string;
  loading: boolean;
}

// Query types (API response data)
export interface QueryTypes {
  meals: MealItem[];
  orders: OrderItem[];
  weeklyMenu: WeeklyMenuData[];
  reviews: ReviewItem[];
}

export interface MealItem {
  id: string;
  name: string;
  description: string;
  type: MealType;
  category: MealCategory;
  price: number;
  image: string;
  rating: number;
  reviewCount: number;
  isAvailable: boolean;
  preparationTime: number;
  calories: number;
  ingredients?: string[];
  allergens?: string[];
}

export interface OrderItem {
  id: string;
  userId: string;
  items: OrderItemDetail[];
  status: OrderStatus;
  total: number;
  orderDate: Date;
  deliveryTime?: Date;
  specialInstructions?: string;
}

export interface OrderItemDetail {
  mealId: string;
  quantity: number;
  size: MealSize;
  customizations?: string[];
}

export interface WeeklyMenuData {
  day: DayOfWeek;
  meals: {
    breakfast: string[];
    lunch: string[];
    dinner: string[];
  };
}

export interface ReviewItem {
  id: string;
  userId: string;
  mealId: string;
  rating: number;
  comment: string;
  date: Date;
  helpful?: number;
}