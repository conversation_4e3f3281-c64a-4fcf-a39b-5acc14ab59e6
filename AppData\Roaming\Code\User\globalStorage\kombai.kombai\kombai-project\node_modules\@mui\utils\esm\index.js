/**
 * @mui/utils v7.3.2
 *
 * @license MIT
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export { default as chainPropTypes } from "./chainPropTypes/index.js";
export { default as deepmerge } from "./deepmerge/index.js";
export { isPlainObject } from "./deepmerge/index.js";
export { default as elementAcceptingRef } from "./elementAcceptingRef/index.js";
export { default as elementTypeAcceptingRef } from "./elementTypeAcceptingRef/index.js";
export { default as exactProp } from "./exactProp/index.js";
export { default as formatMuiErrorMessage } from "./formatMuiErrorMessage/index.js";
export { default as getDisplayName } from "./getDisplayName/index.js";
export { default as HTMLElementType } from "./HTMLElementType/index.js";
export { default as ponyfillGlobal } from "./ponyfillGlobal/index.js";
export { default as refType } from "./refType/index.js";
export { default as unstable_capitalize } from "./capitalize/index.js";
export { default as unstable_createChainedFunction } from "./createChainedFunction/index.js";
export { default as unstable_debounce } from "./debounce/index.js";
export { default as unstable_deprecatedPropType } from "./deprecatedPropType/index.js";
export { default as unstable_isMuiElement } from "./isMuiElement/index.js";
export { default as unstable_ownerDocument } from "./ownerDocument/index.js";
export { default as unstable_ownerWindow } from "./ownerWindow/index.js";
export { default as unstable_requirePropFactory } from "./requirePropFactory/index.js";
export { default as unstable_setRef } from "./setRef/index.js";
export { default as unstable_useEnhancedEffect } from "./useEnhancedEffect/index.js";
export { default as unstable_useId } from "./useId/index.js";
export { default as unstable_unsupportedProp } from "./unsupportedProp/index.js";
export { default as unstable_useControlled } from "./useControlled/index.js";
export { default as unstable_useEventCallback } from "./useEventCallback/index.js";
export { default as unstable_useForkRef } from "./useForkRef/index.js";
export { default as unstable_useLazyRef } from "./useLazyRef/index.js";
export { default as unstable_useTimeout, Timeout as unstable_Timeout } from "./useTimeout/index.js";
export { default as unstable_useOnMount } from "./useOnMount/index.js";
export { default as unstable_useIsFocusVisible } from "./useIsFocusVisible/index.js";
export { default as unstable_isFocusVisible } from "./isFocusVisible/index.js";
export { default as unstable_getScrollbarSize } from "./getScrollbarSize/index.js";
export { default as usePreviousProps } from "./usePreviousProps/index.js";
export { default as getValidReactChildren } from "./getValidReactChildren/index.js";
export { default as visuallyHidden } from "./visuallyHidden/index.js";
export { default as integerPropType } from "./integerPropType/index.js";
export { default as internal_resolveProps } from "./resolveProps/index.js";
export { default as unstable_composeClasses } from "./composeClasses/index.js";
export { default as unstable_generateUtilityClass } from "./generateUtilityClass/index.js";
export { isGlobalState as unstable_isGlobalState } from "./generateUtilityClass/index.js";
export * from "./generateUtilityClass/index.js";
export { default as unstable_generateUtilityClasses } from "./generateUtilityClasses/index.js";
export { default as unstable_ClassNameGenerator } from "./ClassNameGenerator/index.js";
export { default as clamp } from "./clamp/index.js";
export { default as unstable_useSlotProps } from "./useSlotProps/index.js";
export { default as unstable_resolveComponentProps } from "./resolveComponentProps/index.js";
export { default as unstable_extractEventHandlers } from "./extractEventHandlers/index.js";
export { default as unstable_getReactNodeRef } from "./getReactNodeRef/index.js";
export { default as unstable_getReactElementRef } from "./getReactElementRef/index.js";
export * from "./types/index.js";