import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Stack,
  Chip
} from '@mui/material';
import { DashboardStats } from '../../types/interfaces';

interface DashboardProps {
  stats: DashboardStats;
}

const Dashboard: React.FC<DashboardProps> = ({ stats }) => {
  const statCards = [
    {
      title: "Today's Orders",
      value: stats.todayOrders,
      color: 'primary',
      suffix: ''
    },
    {
      title: 'Weekly Orders',
      value: stats.weeklyOrders,
      color: 'secondary',
      suffix: ''
    },
    {
      title: 'Total Meals',
      value: stats.totalMeals,
      color: 'info',
      suffix: ''
    },
    {
      title: 'Average Rating',
      value: stats.avgRating,
      color: 'success',
      suffix: '/5'
    }
  ];

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
        Dashboard Overview
      </Typography>
      
      <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} sx={{ mb: 4 }}>
        {statCards.map((stat, index) => (
          <Card key={index} sx={{ flex: 1 }}>
            <CardContent>
              <Stack spacing={1}>
                <Typography variant="body2" color="text.secondary">
                  {stat.title}
                </Typography>
                <Typography variant="h3" sx={{ fontWeight: 700, color: `${stat.color}.main` }}>
                  {stat.value}{stat.suffix}
                </Typography>
              </Stack>
            </CardContent>
          </Card>
        ))}
      </Stack>

      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Quick Actions
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
            <Chip label="View Today's Menu" clickable color="primary" />
            <Chip label="Check Orders" clickable color="secondary" />
            <Chip label="Weekly Planning" clickable color="info" />
            <Chip label="View Reviews" clickable color="success" />
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Dashboard;