"use strict";

// https://d3js.org/d3-scale/ v4.0.2 Copyright 2010-2021 Mike <PERSON>
!function (n, t) {
  "object" == typeof exports && "undefined" != typeof module ? t(exports, require("../../../lib-vendor/d3-array/src/index.js"), require("../../../lib-vendor/d3-interpolate/src/index.js"), require("../../../lib-vendor/d3-format/src/index.js"), require("../../../lib-vendor/d3-time/src/index.js"), require("../../../lib-vendor/d3-time-format/src/index.js")) : "function" == typeof define && define.amd ? define(["exports", "d3-array", "d3-interpolate", "d3-format", "d3-time", "d3-time-format"], t) : t((n = "undefined" != typeof globalThis ? globalThis : n || self).d3 = n.d3 || {}, n.d3, n.d3, n.d3, n.d3, n.d3);
}(this, function (n, t, r, e, u, i) {
  "use strict";

  function o(n, t) {
    switch (arguments.length) {
      case 0:
        break;
      case 1:
        this.range(n);
        break;
      default:
        this.range(t).domain(n);
    }
    return this;
  }
  function a(n, t) {
    switch (arguments.length) {
      case 0:
        break;
      case 1:
        "function" == typeof n ? this.interpolator(n) : this.range(n);
        break;
      default:
        this.domain(n), "function" == typeof t ? this.interpolator(t) : this.range(t);
    }
    return this;
  }
  const c = Symbol("implicit");
  function l() {
    var n = new t.InternMap(),
      r = [],
      e = [],
      u = c;
    function i(t) {
      let i = n.get(t);
      if (void 0 === i) {
        if (u !== c) return u;
        n.set(t, i = r.push(t) - 1);
      }
      return e[i % e.length];
    }
    return i.domain = function (e) {
      if (!arguments.length) return r.slice();
      r = [], n = new t.InternMap();
      for (const t of e) n.has(t) || n.set(t, r.push(t) - 1);
      return i;
    }, i.range = function (n) {
      return arguments.length ? (e = Array.from(n), i) : e.slice();
    }, i.unknown = function (n) {
      return arguments.length ? (u = n, i) : u;
    }, i.copy = function () {
      return l(r, e).unknown(u);
    }, o.apply(i, arguments), i;
  }
  function f() {
    var n,
      r,
      e = l().unknown(void 0),
      u = e.domain,
      i = e.range,
      a = 0,
      c = 1,
      s = !1,
      p = 0,
      h = 0,
      g = .5;
    function m() {
      var e = u().length,
        o = c < a,
        l = o ? c : a,
        f = o ? a : c;
      n = (f - l) / Math.max(1, e - p + 2 * h), s && (n = Math.floor(n)), l += (f - l - n * (e - p)) * g, r = n * (1 - p), s && (l = Math.round(l), r = Math.round(r));
      var m = t.range(e).map(function (t) {
        return l + n * t;
      });
      return i(o ? m.reverse() : m);
    }
    return delete e.unknown, e.domain = function (n) {
      return arguments.length ? (u(n), m()) : u();
    }, e.range = function (n) {
      return arguments.length ? ([a, c] = n, a = +a, c = +c, m()) : [a, c];
    }, e.rangeRound = function (n) {
      return [a, c] = n, a = +a, c = +c, s = !0, m();
    }, e.bandwidth = function () {
      return r;
    }, e.step = function () {
      return n;
    }, e.round = function (n) {
      return arguments.length ? (s = !!n, m()) : s;
    }, e.padding = function (n) {
      return arguments.length ? (p = Math.min(1, h = +n), m()) : p;
    }, e.paddingInner = function (n) {
      return arguments.length ? (p = Math.min(1, n), m()) : p;
    }, e.paddingOuter = function (n) {
      return arguments.length ? (h = +n, m()) : h;
    }, e.align = function (n) {
      return arguments.length ? (g = Math.max(0, Math.min(1, n)), m()) : g;
    }, e.copy = function () {
      return f(u(), [a, c]).round(s).paddingInner(p).paddingOuter(h).align(g);
    }, o.apply(m(), arguments);
  }
  function s(n) {
    var t = n.copy;
    return n.padding = n.paddingOuter, delete n.paddingInner, delete n.paddingOuter, n.copy = function () {
      return s(t());
    }, n;
  }
  function p(n) {
    return +n;
  }
  var h = [0, 1];
  function g(n) {
    return n;
  }
  function m(n, t) {
    return (t -= n = +n) ? function (r) {
      return (r - n) / t;
    } : (r = isNaN(t) ? NaN : .5, function () {
      return r;
    });
    var r;
  }
  function d(n, t, r) {
    var e = n[0],
      u = n[1],
      i = t[0],
      o = t[1];
    return u < e ? (e = m(u, e), i = r(o, i)) : (e = m(e, u), i = r(i, o)), function (n) {
      return i(e(n));
    };
  }
  function y(n, r, e) {
    var u = Math.min(n.length, r.length) - 1,
      i = new Array(u),
      o = new Array(u),
      a = -1;
    for (n[u] < n[0] && (n = n.slice().reverse(), r = r.slice().reverse()); ++a < u;) i[a] = m(n[a], n[a + 1]), o[a] = e(r[a], r[a + 1]);
    return function (r) {
      var e = t.bisect(n, r, 1, u) - 1;
      return o[e](i[e](r));
    };
  }
  function v(n, t) {
    return t.domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown());
  }
  function M() {
    var n,
      t,
      e,
      u,
      i,
      o,
      a = h,
      c = h,
      l = r.interpolate,
      f = g;
    function s() {
      var n,
        t,
        r,
        e = Math.min(a.length, c.length);
      return f !== g && (n = a[0], t = a[e - 1], n > t && (r = n, n = t, t = r), f = function (r) {
        return Math.max(n, Math.min(t, r));
      }), u = e > 2 ? y : d, i = o = null, m;
    }
    function m(t) {
      return null == t || isNaN(t = +t) ? e : (i || (i = u(a.map(n), c, l)))(n(f(t)));
    }
    return m.invert = function (e) {
      return f(t((o || (o = u(c, a.map(n), r.interpolateNumber)))(e)));
    }, m.domain = function (n) {
      return arguments.length ? (a = Array.from(n, p), s()) : a.slice();
    }, m.range = function (n) {
      return arguments.length ? (c = Array.from(n), s()) : c.slice();
    }, m.rangeRound = function (n) {
      return c = Array.from(n), l = r.interpolateRound, s();
    }, m.clamp = function (n) {
      return arguments.length ? (f = !!n || g, s()) : f !== g;
    }, m.interpolate = function (n) {
      return arguments.length ? (l = n, s()) : l;
    }, m.unknown = function (n) {
      return arguments.length ? (e = n, m) : e;
    }, function (r, e) {
      return n = r, t = e, s();
    };
  }
  function k() {
    return M()(g, g);
  }
  function w(n, r, u, i) {
    var o,
      a = t.tickStep(n, r, u);
    switch ((i = e.formatSpecifier(null == i ? ",f" : i)).type) {
      case "s":
        var c = Math.max(Math.abs(n), Math.abs(r));
        return null != i.precision || isNaN(o = e.precisionPrefix(a, c)) || (i.precision = o), e.formatPrefix(i, c);
      case "":
      case "e":
      case "g":
      case "p":
      case "r":
        null != i.precision || isNaN(o = e.precisionRound(a, Math.max(Math.abs(n), Math.abs(r)))) || (i.precision = o - ("e" === i.type));
        break;
      case "f":
      case "%":
        null != i.precision || isNaN(o = e.precisionFixed(a)) || (i.precision = o - 2 * ("%" === i.type));
    }
    return e.format(i);
  }
  function N(n) {
    var r = n.domain;
    return n.ticks = function (n) {
      var e = r();
      return t.ticks(e[0], e[e.length - 1], null == n ? 10 : n);
    }, n.tickFormat = function (n, t) {
      var e = r();
      return w(e[0], e[e.length - 1], null == n ? 10 : n, t);
    }, n.nice = function (e) {
      null == e && (e = 10);
      var u,
        i,
        o = r(),
        a = 0,
        c = o.length - 1,
        l = o[a],
        f = o[c],
        s = 10;
      for (f < l && (i = l, l = f, f = i, i = a, a = c, c = i); s-- > 0;) {
        if ((i = t.tickIncrement(l, f, e)) === u) return o[a] = l, o[c] = f, r(o);
        if (i > 0) l = Math.floor(l / i) * i, f = Math.ceil(f / i) * i;else {
          if (!(i < 0)) break;
          l = Math.ceil(l * i) / i, f = Math.floor(f * i) / i;
        }
        u = i;
      }
      return n;
    }, n;
  }
  function b(n, t) {
    var r,
      e = 0,
      u = (n = n.slice()).length - 1,
      i = n[e],
      o = n[u];
    return o < i && (r = e, e = u, u = r, r = i, i = o, o = r), n[e] = t.floor(i), n[u] = t.ceil(o), n;
  }
  function x(n) {
    return Math.log(n);
  }
  function q(n) {
    return Math.exp(n);
  }
  function S(n) {
    return -Math.log(-n);
  }
  function A(n) {
    return -Math.exp(-n);
  }
  function D(n) {
    return isFinite(n) ? +("1e" + n) : n < 0 ? 0 : n;
  }
  function I(n) {
    return (t, r) => -n(-t, r);
  }
  function R(n) {
    const r = n(x, q),
      u = r.domain;
    let i,
      o,
      a = 10;
    function c() {
      return i = function (n) {
        return n === Math.E ? Math.log : 10 === n && Math.log10 || 2 === n && Math.log2 || (n = Math.log(n), t => Math.log(t) / n);
      }(a), o = function (n) {
        return 10 === n ? D : n === Math.E ? Math.exp : t => Math.pow(n, t);
      }(a), u()[0] < 0 ? (i = I(i), o = I(o), n(S, A)) : n(x, q), r;
    }
    return r.base = function (n) {
      return arguments.length ? (a = +n, c()) : a;
    }, r.domain = function (n) {
      return arguments.length ? (u(n), c()) : u();
    }, r.ticks = n => {
      const r = u();
      let e = r[0],
        c = r[r.length - 1];
      const l = c < e;
      l && ([e, c] = [c, e]);
      let f,
        s,
        p = i(e),
        h = i(c);
      const g = null == n ? 10 : +n;
      let m = [];
      if (!(a % 1) && h - p < g) {
        if (p = Math.floor(p), h = Math.ceil(h), e > 0) {
          for (; p <= h; ++p) for (f = 1; f < a; ++f) if (s = p < 0 ? f / o(-p) : f * o(p), !(s < e)) {
            if (s > c) break;
            m.push(s);
          }
        } else for (; p <= h; ++p) for (f = a - 1; f >= 1; --f) if (s = p > 0 ? f / o(-p) : f * o(p), !(s < e)) {
          if (s > c) break;
          m.push(s);
        }
        2 * m.length < g && (m = t.ticks(e, c, g));
      } else m = t.ticks(p, h, Math.min(h - p, g)).map(o);
      return l ? m.reverse() : m;
    }, r.tickFormat = (n, t) => {
      if (null == n && (n = 10), null == t && (t = 10 === a ? "s" : ","), "function" != typeof t && (a % 1 || null != (t = e.formatSpecifier(t)).precision || (t.trim = !0), t = e.format(t)), n === 1 / 0) return t;
      const u = Math.max(1, a * n / r.ticks().length);
      return n => {
        let r = n / o(Math.round(i(n)));
        return r * a < a - .5 && (r *= a), r <= u ? t(n) : "";
      };
    }, r.nice = () => u(b(u(), {
      floor: n => o(Math.floor(i(n))),
      ceil: n => o(Math.ceil(i(n)))
    })), r;
  }
  function T(n) {
    return function (t) {
      return Math.sign(t) * Math.log1p(Math.abs(t / n));
    };
  }
  function O(n) {
    return function (t) {
      return Math.sign(t) * Math.expm1(Math.abs(t)) * n;
    };
  }
  function F(n) {
    var t = 1,
      r = n(T(t), O(t));
    return r.constant = function (r) {
      return arguments.length ? n(T(t = +r), O(t)) : t;
    }, N(r);
  }
  function P(n) {
    return function (t) {
      return t < 0 ? -Math.pow(-t, n) : Math.pow(t, n);
    };
  }
  function E(n) {
    return n < 0 ? -Math.sqrt(-n) : Math.sqrt(n);
  }
  function L(n) {
    return n < 0 ? -n * n : n * n;
  }
  function Q(n) {
    var t = n(g, g),
      r = 1;
    function e() {
      return 1 === r ? n(g, g) : .5 === r ? n(E, L) : n(P(r), P(1 / r));
    }
    return t.exponent = function (n) {
      return arguments.length ? (r = +n, e()) : r;
    }, N(t);
  }
  function U() {
    var n = Q(M());
    return n.copy = function () {
      return v(n, U()).exponent(n.exponent());
    }, o.apply(n, arguments), n;
  }
  function Y(n) {
    return Math.sign(n) * n * n;
  }
  function j(n) {
    return Math.sign(n) * Math.sqrt(Math.abs(n));
  }
  function B(n) {
    return new Date(n);
  }
  function C(n) {
    return n instanceof Date ? +n : +new Date(+n);
  }
  function H(n, t, r, e, u, i, o, a, c, l) {
    var f = k(),
      s = f.invert,
      p = f.domain,
      h = l(".%L"),
      g = l(":%S"),
      m = l("%I:%M"),
      d = l("%I %p"),
      y = l("%a %d"),
      M = l("%b %d"),
      w = l("%B"),
      N = l("%Y");
    function x(n) {
      return (c(n) < n ? h : a(n) < n ? g : o(n) < n ? m : i(n) < n ? d : e(n) < n ? u(n) < n ? y : M : r(n) < n ? w : N)(n);
    }
    return f.invert = function (n) {
      return new Date(s(n));
    }, f.domain = function (n) {
      return arguments.length ? p(Array.from(n, C)) : p().map(B);
    }, f.ticks = function (t) {
      var r = p();
      return n(r[0], r[r.length - 1], null == t ? 10 : t);
    }, f.tickFormat = function (n, t) {
      return null == t ? x : l(t);
    }, f.nice = function (n) {
      var r = p();
      return n && "function" == typeof n.range || (n = t(r[0], r[r.length - 1], null == n ? 10 : n)), n ? p(b(r, n)) : f;
    }, f.copy = function () {
      return v(f, H(n, t, r, e, u, i, o, a, c, l));
    }, f;
  }
  function W() {
    var n,
      t,
      e,
      u,
      i,
      o = 0,
      a = 1,
      c = g,
      l = !1;
    function f(t) {
      return null == t || isNaN(t = +t) ? i : c(0 === e ? .5 : (t = (u(t) - n) * e, l ? Math.max(0, Math.min(1, t)) : t));
    }
    function s(n) {
      return function (t) {
        var r, e;
        return arguments.length ? ([r, e] = t, c = n(r, e), f) : [c(0), c(1)];
      };
    }
    return f.domain = function (r) {
      return arguments.length ? ([o, a] = r, n = u(o = +o), t = u(a = +a), e = n === t ? 0 : 1 / (t - n), f) : [o, a];
    }, f.clamp = function (n) {
      return arguments.length ? (l = !!n, f) : l;
    }, f.interpolator = function (n) {
      return arguments.length ? (c = n, f) : c;
    }, f.range = s(r.interpolate), f.rangeRound = s(r.interpolateRound), f.unknown = function (n) {
      return arguments.length ? (i = n, f) : i;
    }, function (r) {
      return u = r, n = r(o), t = r(a), e = n === t ? 0 : 1 / (t - n), f;
    };
  }
  function _(n, t) {
    return t.domain(n.domain()).interpolator(n.interpolator()).clamp(n.clamp()).unknown(n.unknown());
  }
  function z() {
    var n = Q(W());
    return n.copy = function () {
      return _(n, z()).exponent(n.exponent());
    }, a.apply(n, arguments);
  }
  function G() {
    var n,
      t,
      e,
      u,
      i,
      o,
      a,
      c = 0,
      l = .5,
      f = 1,
      s = 1,
      p = g,
      h = !1;
    function m(n) {
      return isNaN(n = +n) ? a : (n = .5 + ((n = +o(n)) - t) * (s * n < s * t ? u : i), p(h ? Math.max(0, Math.min(1, n)) : n));
    }
    function d(n) {
      return function (t) {
        var e, u, i;
        return arguments.length ? ([e, u, i] = t, p = r.piecewise(n, [e, u, i]), m) : [p(0), p(.5), p(1)];
      };
    }
    return m.domain = function (r) {
      return arguments.length ? ([c, l, f] = r, n = o(c = +c), t = o(l = +l), e = o(f = +f), u = n === t ? 0 : .5 / (t - n), i = t === e ? 0 : .5 / (e - t), s = t < n ? -1 : 1, m) : [c, l, f];
    }, m.clamp = function (n) {
      return arguments.length ? (h = !!n, m) : h;
    }, m.interpolator = function (n) {
      return arguments.length ? (p = n, m) : p;
    }, m.range = d(r.interpolate), m.rangeRound = d(r.interpolateRound), m.unknown = function (n) {
      return arguments.length ? (a = n, m) : a;
    }, function (r) {
      return o = r, n = r(c), t = r(l), e = r(f), u = n === t ? 0 : .5 / (t - n), i = t === e ? 0 : .5 / (e - t), s = t < n ? -1 : 1, m;
    };
  }
  function J() {
    var n = Q(G());
    return n.copy = function () {
      return _(n, J()).exponent(n.exponent());
    }, a.apply(n, arguments);
  }
  n.scaleBand = f, n.scaleDiverging = function n() {
    var t = N(G()(g));
    return t.copy = function () {
      return _(t, n());
    }, a.apply(t, arguments);
  }, n.scaleDivergingLog = function n() {
    var t = R(G()).domain([.1, 1, 10]);
    return t.copy = function () {
      return _(t, n()).base(t.base());
    }, a.apply(t, arguments);
  }, n.scaleDivergingPow = J, n.scaleDivergingSqrt = function () {
    return J.apply(null, arguments).exponent(.5);
  }, n.scaleDivergingSymlog = function n() {
    var t = F(G());
    return t.copy = function () {
      return _(t, n()).constant(t.constant());
    }, a.apply(t, arguments);
  }, n.scaleIdentity = function n(t) {
    var r;
    function e(n) {
      return null == n || isNaN(n = +n) ? r : n;
    }
    return e.invert = e, e.domain = e.range = function (n) {
      return arguments.length ? (t = Array.from(n, p), e) : t.slice();
    }, e.unknown = function (n) {
      return arguments.length ? (r = n, e) : r;
    }, e.copy = function () {
      return n(t).unknown(r);
    }, t = arguments.length ? Array.from(t, p) : [0, 1], N(e);
  }, n.scaleImplicit = c, n.scaleLinear = function n() {
    var t = k();
    return t.copy = function () {
      return v(t, n());
    }, o.apply(t, arguments), N(t);
  }, n.scaleLog = function n() {
    const t = R(M()).domain([1, 10]);
    return t.copy = () => v(t, n()).base(t.base()), o.apply(t, arguments), t;
  }, n.scaleOrdinal = l, n.scalePoint = function () {
    return s(f.apply(null, arguments).paddingInner(1));
  }, n.scalePow = U, n.scaleQuantile = function n() {
    var r,
      e = [],
      u = [],
      i = [];
    function a() {
      var n = 0,
        r = Math.max(1, u.length);
      for (i = new Array(r - 1); ++n < r;) i[n - 1] = t.quantileSorted(e, n / r);
      return c;
    }
    function c(n) {
      return null == n || isNaN(n = +n) ? r : u[t.bisect(i, n)];
    }
    return c.invertExtent = function (n) {
      var t = u.indexOf(n);
      return t < 0 ? [NaN, NaN] : [t > 0 ? i[t - 1] : e[0], t < i.length ? i[t] : e[e.length - 1]];
    }, c.domain = function (n) {
      if (!arguments.length) return e.slice();
      e = [];
      for (let t of n) null == t || isNaN(t = +t) || e.push(t);
      return e.sort(t.ascending), a();
    }, c.range = function (n) {
      return arguments.length ? (u = Array.from(n), a()) : u.slice();
    }, c.unknown = function (n) {
      return arguments.length ? (r = n, c) : r;
    }, c.quantiles = function () {
      return i.slice();
    }, c.copy = function () {
      return n().domain(e).range(u).unknown(r);
    }, o.apply(c, arguments);
  }, n.scaleQuantize = function n() {
    var r,
      e = 0,
      u = 1,
      i = 1,
      a = [.5],
      c = [0, 1];
    function l(n) {
      return null != n && n <= n ? c[t.bisect(a, n, 0, i)] : r;
    }
    function f() {
      var n = -1;
      for (a = new Array(i); ++n < i;) a[n] = ((n + 1) * u - (n - i) * e) / (i + 1);
      return l;
    }
    return l.domain = function (n) {
      return arguments.length ? ([e, u] = n, e = +e, u = +u, f()) : [e, u];
    }, l.range = function (n) {
      return arguments.length ? (i = (c = Array.from(n)).length - 1, f()) : c.slice();
    }, l.invertExtent = function (n) {
      var t = c.indexOf(n);
      return t < 0 ? [NaN, NaN] : t < 1 ? [e, a[0]] : t >= i ? [a[i - 1], u] : [a[t - 1], a[t]];
    }, l.unknown = function (n) {
      return arguments.length ? (r = n, l) : l;
    }, l.thresholds = function () {
      return a.slice();
    }, l.copy = function () {
      return n().domain([e, u]).range(c).unknown(r);
    }, o.apply(N(l), arguments);
  }, n.scaleRadial = function n() {
    var t,
      r = k(),
      e = [0, 1],
      u = !1;
    function i(n) {
      var e = j(r(n));
      return isNaN(e) ? t : u ? Math.round(e) : e;
    }
    return i.invert = function (n) {
      return r.invert(Y(n));
    }, i.domain = function (n) {
      return arguments.length ? (r.domain(n), i) : r.domain();
    }, i.range = function (n) {
      return arguments.length ? (r.range((e = Array.from(n, p)).map(Y)), i) : e.slice();
    }, i.rangeRound = function (n) {
      return i.range(n).round(!0);
    }, i.round = function (n) {
      return arguments.length ? (u = !!n, i) : u;
    }, i.clamp = function (n) {
      return arguments.length ? (r.clamp(n), i) : r.clamp();
    }, i.unknown = function (n) {
      return arguments.length ? (t = n, i) : t;
    }, i.copy = function () {
      return n(r.domain(), e).round(u).clamp(r.clamp()).unknown(t);
    }, o.apply(i, arguments), N(i);
  }, n.scaleSequential = function n() {
    var t = N(W()(g));
    return t.copy = function () {
      return _(t, n());
    }, a.apply(t, arguments);
  }, n.scaleSequentialLog = function n() {
    var t = R(W()).domain([1, 10]);
    return t.copy = function () {
      return _(t, n()).base(t.base());
    }, a.apply(t, arguments);
  }, n.scaleSequentialPow = z, n.scaleSequentialQuantile = function n() {
    var r = [],
      e = g;
    function u(n) {
      if (null != n && !isNaN(n = +n)) return e((t.bisect(r, n, 1) - 1) / (r.length - 1));
    }
    return u.domain = function (n) {
      if (!arguments.length) return r.slice();
      r = [];
      for (let t of n) null == t || isNaN(t = +t) || r.push(t);
      return r.sort(t.ascending), u;
    }, u.interpolator = function (n) {
      return arguments.length ? (e = n, u) : e;
    }, u.range = function () {
      return r.map((n, t) => e(t / (r.length - 1)));
    }, u.quantiles = function (n) {
      return Array.from({
        length: n + 1
      }, (e, u) => t.quantile(r, u / n));
    }, u.copy = function () {
      return n(e).domain(r);
    }, a.apply(u, arguments);
  }, n.scaleSequentialSqrt = function () {
    return z.apply(null, arguments).exponent(.5);
  }, n.scaleSequentialSymlog = function n() {
    var t = F(W());
    return t.copy = function () {
      return _(t, n()).constant(t.constant());
    }, a.apply(t, arguments);
  }, n.scaleSqrt = function () {
    return U.apply(null, arguments).exponent(.5);
  }, n.scaleSymlog = function n() {
    var t = F(M());
    return t.copy = function () {
      return v(t, n()).constant(t.constant());
    }, o.apply(t, arguments);
  }, n.scaleThreshold = function n() {
    var r,
      e = [.5],
      u = [0, 1],
      i = 1;
    function a(n) {
      return null != n && n <= n ? u[t.bisect(e, n, 0, i)] : r;
    }
    return a.domain = function (n) {
      return arguments.length ? (e = Array.from(n), i = Math.min(e.length, u.length - 1), a) : e.slice();
    }, a.range = function (n) {
      return arguments.length ? (u = Array.from(n), i = Math.min(e.length, u.length - 1), a) : u.slice();
    }, a.invertExtent = function (n) {
      var t = u.indexOf(n);
      return [e[t - 1], e[t]];
    }, a.unknown = function (n) {
      return arguments.length ? (r = n, a) : r;
    }, a.copy = function () {
      return n().domain(e).range(u).unknown(r);
    }, o.apply(a, arguments);
  }, n.scaleTime = function () {
    return o.apply(H(u.timeTicks, u.timeTickInterval, u.timeYear, u.timeMonth, u.timeWeek, u.timeDay, u.timeHour, u.timeMinute, u.timeSecond, i.timeFormat).domain([new Date(2e3, 0, 1), new Date(2e3, 0, 2)]), arguments);
  }, n.scaleUtc = function () {
    return o.apply(H(u.utcTicks, u.utcTickInterval, u.utcYear, u.utcMonth, u.utcWeek, u.utcDay, u.utcHour, u.utcMinute, u.utcSecond, i.utcFormat).domain([Date.UTC(2e3, 0, 1), Date.UTC(2e3, 0, 2)]), arguments);
  }, n.tickFormat = w, Object.defineProperty(n, "__esModule", {
    value: !0
  });
});