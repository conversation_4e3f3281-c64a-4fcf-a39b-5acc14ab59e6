import * as React from 'react';
import { SxProps, Theme } from '@mui/system';
export type GridFooterContainerProps = React.HTMLAttributes<HTMLDivElement> & {
  sx?: SxProps<Theme>;
};
declare const GridFooterContainer: React.ForwardRefExoticComponent<GridFooterContainerProps> | React.ForwardRefExoticComponent<React.HTMLAttributes<HTMLDivElement> & {
  sx?: SxProps<Theme>;
} & React.RefAttributes<HTMLDivElement>>;
export { GridFooterContainer };