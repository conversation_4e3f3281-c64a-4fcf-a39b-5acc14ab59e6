import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  <PERSON>ack,
  <PERSON>ing,
  Button,
  TextField,
  Avatar,
  Divider,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { ReviewItem } from '../../types/interfaces';

interface ReviewsProps {
  reviews: ReviewItem[];
  onSubmitReview: (review: Omit<ReviewItem, 'id' | 'date'>) => void;
}

const Reviews: React.FC<ReviewsProps> = ({ reviews, onSubmitReview }) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [newReview, setNewReview] = useState({
    rating: 0,
    comment: '',
    mealId: 'meal-1', // Default meal ID
    userId: 'user-1' // Default user ID
  });

  const handleSubmitReview = () => {
    if (newReview.rating > 0 && newReview.comment.trim()) {
      onSubmitReview({
        ...newReview,
        helpful: 0
      });
      setNewReview({
        rating: 0,
        comment: '',
        mealId: 'meal-1',
        userId: 'user-1'
      });
      setDialogOpen(false);
    }
  };

  const getAverageRating = () => {
    if (reviews.length === 0) return 0;
    const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
    return (sum / reviews.length).toFixed(1);
  };

  const getRatingDistribution = () => {
    const distribution = [0, 0, 0, 0, 0]; // 1-5 stars
    reviews.forEach(review => {
      distribution[review.rating - 1]++;
    });
    return distribution;
  };

  const getMealName = (mealId: string) => {
    const mealNames: { [key: string]: string } = {
      'meal-1': 'Paneer Butter Masala',
      'meal-2': 'Masala Dosa',
      'meal-3': 'Chicken Biryani',
    };
    return mealNames[mealId] || 'Unknown Meal';
  };

  const getUserName = (userId: string) => {
    // In a real app, you'd fetch user details
    return userId === 'user-1' ? 'John Doe' : 'Anonymous User';
  };

  const distribution = getRatingDistribution();

  return (
    <Box>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Reviews & Ratings
        </Typography>
        <Button variant="contained" onClick={() => setDialogOpen(true)}>
          Write a Review
        </Button>
      </Stack>

      {/* Rating Summary */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={4} alignItems="center">
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h2" sx={{ fontWeight: 700, color: 'primary.main' }}>
                {getAverageRating()}
              </Typography>
              <Rating value={parseFloat(getAverageRating())} precision={0.1} readOnly />
              <Typography variant="body2" color="text.secondary">
                Based on {reviews.length} review{reviews.length !== 1 ? 's' : ''}
              </Typography>
            </Box>

            <Box sx={{ flex: 1 }}>
              {[5, 4, 3, 2, 1].map((star) => (
                <Stack key={star} direction="row" alignItems="center" spacing={1}>
                  <Typography variant="body2" sx={{ minWidth: 20 }}>
                    {star}
                  </Typography>
                  <Box
                    sx={{
                      flex: 1,
                      height: 8,
                      backgroundColor: 'grey.200',
                      borderRadius: 1,
                      overflow: 'hidden'
                    }}
                  >
                    <Box
                      sx={{
                        height: '100%',
                        backgroundColor: 'primary.main',
                        width: `${reviews.length > 0 ? (distribution[star - 1] / reviews.length) * 100 : 0}%`,
                        transition: 'width 0.3s ease'
                      }}
                    />
                  </Box>
                  <Typography variant="body2" sx={{ minWidth: 30 }}>
                    {distribution[star - 1]}
                  </Typography>
                </Stack>
              ))}
            </Box>
          </Stack>
        </CardContent>
      </Card>

      {/* Reviews List */}
      <Stack spacing={2}>
        {reviews.map((review) => (
          <Card key={review.id}>
            <CardContent>
              <Stack direction="row" spacing={2}>
                <Avatar sx={{ width: 40, height: 40 }}>
                  {getUserName(review.userId).charAt(0)}
                </Avatar>
                
                <Box sx={{ flex: 1 }}>
                  <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                    <Box>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                        {getUserName(review.userId)}
                      </Typography>
                      <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                        <Rating value={review.rating} size="small" readOnly />
                        <Typography variant="caption" color="text.secondary">
                          {review.date.toLocaleDateString()}
                        </Typography>
                      </Stack>
                    </Box>
                    
                    <Chip
                      label={getMealName(review.mealId)}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Stack>
                  
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    {review.comment}
                  </Typography>
                  
                  {review.helpful !== undefined && review.helpful > 0 && (
                    <Typography variant="caption" color="text.secondary">
                      {review.helpful} people found this helpful
                    </Typography>
                  )}
                </Box>
              </Stack>
            </CardContent>
          </Card>
        ))}
      </Stack>

      {reviews.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No reviews yet
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Be the first to share your experience!
          </Typography>
          <Button variant="contained" onClick={() => setDialogOpen(true)}>
            Write First Review
          </Button>
        </Box>
      )}

      {/* Write Review Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Write a Review</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Rating
              </Typography>
              <Rating
                value={newReview.rating}
                onChange={(_, value) => setNewReview({ ...newReview, rating: value || 0 })}
                size="large"
              />
            </Box>
            
            <TextField
              label="Your Review"
              multiline
              rows={4}
              fullWidth
              value={newReview.comment}
              onChange={(e) => setNewReview({ ...newReview, comment: e.target.value })}
              placeholder="Share your experience with this meal..."
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSubmitReview}
            variant="contained"
            disabled={newReview.rating === 0 || !newReview.comment.trim()}
          >
            Submit Review
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Reviews;