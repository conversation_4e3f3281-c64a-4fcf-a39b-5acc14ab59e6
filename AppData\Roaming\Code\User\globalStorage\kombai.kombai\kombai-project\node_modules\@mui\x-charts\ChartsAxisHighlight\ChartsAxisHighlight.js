"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ChartsAxisHighlight = ChartsAxisHighlight;
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _chartsAxisHighlightClasses = require("./chartsAxisHighlightClasses");
var _ChartsYAxisHighlight = _interopRequireDefault(require("./ChartsYAxisHighlight"));
var _ChartsXAxisHighlight = _interopRequireDefault(require("./ChartsXAxisHighlight"));
var _jsxRuntime = require("react/jsx-runtime");
const useUtilityClasses = () => {
  const slots = {
    root: ['root']
  };
  return (0, _composeClasses.default)(slots, _chartsAxisHighlightClasses.getAxisHighlightUtilityClass);
};

/**
 * Demos:
 *
 * - [Custom components](https://mui.com/x/react-charts/components/)
 *
 * API:
 *
 * - [ChartsAxisHighlight API](https://mui.com/x/api/charts/charts-axis-highlight/)
 */
function ChartsAxisHighlight(props) {
  const {
    x: xAxisHighlight,
    y: yAxisHighlight
  } = props;
  const classes = useUtilityClasses();
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {
    children: [xAxisHighlight && xAxisHighlight !== 'none' && /*#__PURE__*/(0, _jsxRuntime.jsx)(_ChartsXAxisHighlight.default, {
      type: xAxisHighlight,
      classes: classes
    }), yAxisHighlight && yAxisHighlight !== 'none' && /*#__PURE__*/(0, _jsxRuntime.jsx)(_ChartsYAxisHighlight.default, {
      type: yAxisHighlight,
      classes: classes
    })]
  });
}
process.env.NODE_ENV !== "production" ? ChartsAxisHighlight.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  x: _propTypes.default.oneOf(['band', 'line', 'none']),
  y: _propTypes.default.oneOf(['band', 'line', 'none'])
} : void 0;