import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Stack,
  Chip,
  Button,
  Paper,
  IconButton
} from '@mui/material';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { WeeklyMenuData } from '../../types/interfaces';
import { DayOfWeek, MealType } from '../../types/enums';

interface WeeklyPlannerProps {
  weeklyMenu: WeeklyMenuData[];
  onUpdateMenu: (day: DayOfWeek, mealType: MealType, mealIds: string[]) => void;
}

const WeeklyPlanner: React.FC<WeeklyPlannerProps> = ({ weeklyMenu, onUpdateMenu }) => {
  const [currentWeek, setCurrentWeek] = useState(new Date());

  const daysOfWeek = [
    { key: DayOfWeek.MONDAY, label: 'Monday' },
    { key: DayOfWeek.TUESDAY, label: 'Tuesday' },
    { key: DayOfWeek.WEDNESDAY, label: 'Wednesday' },
    { key: DayOfWeek.THURSDAY, label: 'Thursday' },
    { key: DayOfWeek.FRIDAY, label: 'Friday' },
    { key: DayOfWeek.SATURDAY, label: 'Saturday' },
    { key: DayOfWeek.SUNDAY, label: 'Sunday' },
  ];

  const mealTypes = [
    { key: MealType.BREAKFAST, label: 'Breakfast', color: 'warning' },
    { key: MealType.LUNCH, label: 'Lunch', color: 'primary' },
    { key: MealType.DINNER, label: 'Dinner', color: 'secondary' },
  ];

  const getMenuForDay = (day: DayOfWeek) => {
    return weeklyMenu.find(menu => menu.day === day);
  };

  const getMealName = (mealId: string) => {
    // In a real app, you'd fetch meal details by ID
    const mealNames: { [key: string]: string } = {
      'meal-1': 'Paneer Butter Masala',
      'meal-2': 'Masala Dosa',
      'meal-3': 'Chicken Biryani',
    };
    return mealNames[mealId] || `Meal ${mealId}`;
  };

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentWeek);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    setCurrentWeek(newDate);
  };

  const getWeekRange = () => {
    const startOfWeek = new Date(currentWeek);
    startOfWeek.setDate(currentWeek.getDate() - currentWeek.getDay() + 1);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    
    return `${startOfWeek.toLocaleDateString()} - ${endOfWeek.toLocaleDateString()}`;
  };

  return (
    <Box>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Weekly Menu Planner
        </Typography>
        
        <Stack direction="row" alignItems="center" spacing={2}>
          <IconButton onClick={() => navigateWeek('prev')}>
            <NavigateBeforeIcon />
          </IconButton>
          <Typography variant="h6">
            {getWeekRange()}
          </Typography>
          <IconButton onClick={() => navigateWeek('next')}>
            <NavigateNextIcon />
          </IconButton>
        </Stack>
      </Stack>

      <Box sx={{ overflowX: 'auto' }}>
        <Stack direction="row" spacing={2} sx={{ minWidth: 1200 }}>
          {daysOfWeek.map((day) => {
            const dayMenu = getMenuForDay(day.key);
            
            return (
              <Card key={day.key} sx={{ minWidth: 160, flex: 1 }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2, textAlign: 'center', fontWeight: 600 }}>
                    {day.label}
                  </Typography>
                  
                  <Stack spacing={2}>
                    {mealTypes.map((mealType) => {
                      const meals = dayMenu?.meals[mealType.key as keyof typeof dayMenu.meals] || [];
                      
                      return (
                        <Paper key={mealType.key} sx={{ p: 2, backgroundColor: 'grey.50' }}>
                          <Typography variant="subtitle2" sx={{ mb: 1, color: `${mealType.color}.main` }}>
                            {mealType.label}
                          </Typography>
                          
                          <Stack spacing={1}>
                            {meals.length > 0 ? (
                              meals.map((mealId, index) => (
                                <Chip
                                  key={index}
                                  label={getMealName(mealId)}
                                  size="small"
                                  color={mealType.color as any}
                                  variant="outlined"
                                />
                              ))
                            ) : (
                              <Typography variant="caption" color="text.secondary">
                                No meals planned
                              </Typography>
                            )}
                            
                            <Button
                              size="small"
                              variant="outlined"
                              color={mealType.color as any}
                              sx={{ mt: 1 }}
                              onClick={() => {
                                // In a real app, this would open a meal selection dialog
                                console.log(`Add meal for ${day.label} ${mealType.label}`);
                              }}
                            >
                              + Add Meal
                            </Button>
                          </Stack>
                        </Paper>
                      );
                    })}
                  </Stack>
                </CardContent>
              </Card>
            );
          })}
        </Stack>
      </Box>

      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Quick Actions
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
            <Button variant="outlined" color="primary">
              Import Previous Week
            </Button>
            <Button variant="outlined" color="secondary">
              Generate Auto Menu
            </Button>
            <Button variant="outlined" color="info">
              Export Menu
            </Button>
            <Button variant="contained" color="success">
              Publish Menu
            </Button>
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );
};

export default WeeklyPlanner;