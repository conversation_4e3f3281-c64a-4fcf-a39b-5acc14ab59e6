import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Divider,
  Typography,
  Box
} from '@mui/material';
import BentoOutlinedIcon from '@mui/icons-material/BentoOutlined';
import FreeBreakfastOutlinedIcon from '@mui/icons-material/FreeBreakfastOutlined';
import RestaurantOutlinedIcon from '@mui/icons-material/RestaurantOutlined';
import ModeNightOutlinedIcon from '@mui/icons-material/ModeNightOutlined';
import ShoppingCartOutlinedIcon from '@mui/icons-material/ShoppingCartOutlined';
import CalendarMonthOutlinedIcon from '@mui/icons-material/CalendarMonthOutlined';
import PhotoLibraryOutlinedIcon from '@mui/icons-material/PhotoLibraryOutlined';
import RateReviewOutlinedIcon from '@mui/icons-material/RateReviewOutlined';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';
import AnalyticsOutlinedIcon from '@mui/icons-material/AnalyticsOutlined';

const drawerWidth = 280;

interface SidebarProps {
  open: boolean;
  currentView: string;
  onViewChange: (view: string) => void;
}

const menuItems = [
  { id: 'dashboard', label: 'Dashboard', icon: BentoOutlinedIcon },
  { id: 'breakfast', label: 'Breakfast', icon: FreeBreakfastOutlinedIcon },
  { id: 'lunch', label: 'Lunch', icon: RestaurantOutlinedIcon },
  { id: 'dinner', label: 'Dinner', icon: ModeNightOutlinedIcon },
];

const managementItems = [
  { id: 'orders', label: 'Order Management', icon: ShoppingCartOutlinedIcon },
  { id: 'weekly-menu', label: 'Weekly Menu', icon: CalendarMonthOutlinedIcon },
  { id: 'gallery', label: 'Image Gallery', icon: PhotoLibraryOutlinedIcon },
  { id: 'reviews', label: 'Reviews', icon: RateReviewOutlinedIcon },
];

const systemItems = [
  { id: 'analytics', label: 'Analytics', icon: AnalyticsOutlinedIcon },
  { id: 'settings', label: 'Settings', icon: SettingsOutlinedIcon },
];

const Sidebar: React.FC<SidebarProps> = ({ open, currentView, onViewChange }) => {
  const renderMenuSection = (title: string, items: typeof menuItems) => (
    <Box>
      <Typography
        variant="overline"
        sx={{
          px: 2,
          py: 1,
          color: 'text.secondary',
          fontWeight: 600,
          fontSize: '0.75rem'
        }}
      >
        {title}
      </Typography>
      <List sx={{ py: 0 }}>
        {items.map((item) => {
          const Icon = item.icon;
          const isSelected = currentView === item.id;
          
          return (
            <ListItem key={item.id} disablePadding>
              <ListItemButton
                selected={isSelected}
                onClick={() => onViewChange(item.id)}
                sx={{
                  mx: 1,
                  borderRadius: 2,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                  },
                }}
              >
                <ListItemIcon sx={{ color: isSelected ? 'inherit' : 'text.secondary' }}>
                  <Icon />
                </ListItemIcon>
                <ListItemText primary={item.label} />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
    </Box>
  );

  return (
    <Drawer
      variant="persistent"
      open={open}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
        },
      }}
    >
      <Toolbar />
      <Box sx={{ overflow: 'auto', py: 1 }}>
        {renderMenuSection('Meals', menuItems)}
        <Divider sx={{ my: 2 }} />
        {renderMenuSection('Management', managementItems)}
        <Divider sx={{ my: 2 }} />
        {renderMenuSection('System', systemItems)}
      </Box>
    </Drawer>
  );
};

export default Sidebar;