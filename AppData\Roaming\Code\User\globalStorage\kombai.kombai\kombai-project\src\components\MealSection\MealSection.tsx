import React, { useState } from 'react';
import {
  Box,
  Typography,
  Stack,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Pagination
} from '@mui/material';
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
import MealCard from '../MealCard/MealCard';
import { MealItem } from '../../types/interfaces';
import { MealType, MealSize } from '../../types/enums';

interface MealSectionProps {
  title: string;
  meals: MealItem[];
  mealType: MealType;
  onAddToCart: (meal: MealItem, size: MealSize, quantity: number) => void;
  onViewDetails: (meal: MealItem) => void;
}

const MealSection: React.FC<MealSectionProps> = ({
  title,
  meals,
  mealType,
  onAddToCart,
  onViewDetails
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [filterBy, setFilterBy] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  // Filter meals by type and search/filter criteria
  const filteredMeals = meals
    .filter(meal => meal.type === mealType)
    .filter(meal => 
      meal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      meal.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter(meal => filterBy === 'all' || meal.category === filterBy)
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price':
          return a.price - b.price;
        case 'rating':
          return b.rating - a.rating;
        case 'calories':
          return a.calories - b.calories;
        default:
          return 0;
      }
    });

  // Pagination
  const totalPages = Math.ceil(filteredMeals.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedMeals = filteredMeals.slice(startIndex, startIndex + itemsPerPage);

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setCurrentPage(value);
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
        {title}
      </Typography>

      {/* Search and Filter Controls */}
      <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mb: 3 }}>
        <TextField
          placeholder={`Search ${title.toLowerCase()}...`}
          variant="outlined"
          size="small"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: <SearchOutlinedIcon sx={{ color: 'action.active', mr: 1 }} />,
          }}
          sx={{ flex: 1 }}
        />

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Sort by</InputLabel>
          <Select
            value={sortBy}
            label="Sort by"
            onChange={(e) => setSortBy(e.target.value)}
          >
            <MenuItem value="name">Name</MenuItem>
            <MenuItem value="price">Price</MenuItem>
            <MenuItem value="rating">Rating</MenuItem>
            <MenuItem value="calories">Calories</MenuItem>
          </Select>
        </FormControl>

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Filter</InputLabel>
          <Select
            value={filterBy}
            label="Filter"
            onChange={(e) => setFilterBy(e.target.value)}
          >
            <MenuItem value="all">All</MenuItem>
            <MenuItem value="vegetarian">Vegetarian</MenuItem>
            <MenuItem value="non_vegetarian">Non-Vegetarian</MenuItem>
            <MenuItem value="vegan">Vegan</MenuItem>
            <MenuItem value="gluten_free">Gluten Free</MenuItem>
          </Select>
        </FormControl>
      </Stack>

      {/* Meal Cards Grid */}
      <Stack
        direction="row"
        flexWrap="wrap"
        spacing={3}
        useFlexGap
        sx={{ mb: 3 }}
      >
        {paginatedMeals.map((meal) => (
          <Box key={meal.id} sx={{ flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(33.333% - 16px)' } }}>
            <MealCard
              meal={meal}
              onAddToCart={onAddToCart}
              onViewDetails={onViewDetails}
            />
          </Box>
        ))}
      </Stack>

      {/* Pagination */}
      {totalPages > 1 && (
        <Stack alignItems="center">
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            color="primary"
          />
        </Stack>
      )}

      {filteredMeals.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No {title.toLowerCase()} items found
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default MealSection;