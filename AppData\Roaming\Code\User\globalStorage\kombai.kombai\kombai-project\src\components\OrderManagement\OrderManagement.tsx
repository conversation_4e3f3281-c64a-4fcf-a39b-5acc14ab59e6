import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON>ack,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import { OrderItem } from '../../types/interfaces';
import { OrderStatus } from '../../types/enums';

interface OrderManagementProps {
  orders: OrderItem[];
  onCancelOrder: (orderId: string) => void;
  onViewOrder: (order: OrderItem) => void;
}

const OrderManagement: React.FC<OrderManagementProps> = ({
  orders,
  onCancelOrder,
  onViewOrder
}) => {
  const [selectedOrder, setSelectedOrder] = useState<OrderItem | null>(null);
  const [dialogO<PERSON>, setDialogOpen] = useState(false);

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING: return 'warning';
      case OrderStatus.CONFIRMED: return 'info';
      case OrderStatus.PREPARING: return 'primary';
      case OrderStatus.READY: return 'success';
      case OrderStatus.DELIVERED: return 'success';
      case OrderStatus.CANCELLED: return 'error';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: OrderStatus) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const handleViewOrder = (order: OrderItem) => {
    setSelectedOrder(order);
    setDialogOpen(true);
    onViewOrder(order);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedOrder(null);
  };

  const canCancelOrder = (status: OrderStatus) => {
    return status === OrderStatus.PENDING || status === OrderStatus.CONFIRMED;
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 600 }}>
        Order Management
      </Typography>

      <Card>
        <CardContent>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Order ID</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Items</TableCell>
                  <TableCell>Total</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Delivery Time</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        #{order.id.slice(-6).toUpperCase()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {order.orderDate.toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {order.items.length} item{order.items.length > 1 ? 's' : ''}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        ₹{order.total}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(order.status)}
                        color={getStatusColor(order.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {order.deliveryTime ? (
                        <Typography variant="body2">
                          {order.deliveryTime.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          TBD
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell align="center">
                      <Stack direction="row" spacing={1} justifyContent="center">
                        <IconButton
                          size="small"
                          onClick={() => handleViewOrder(order)}
                        >
                          <VisibilityOutlinedIcon />
                        </IconButton>
                        {canCancelOrder(order.status) && (
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => onCancelOrder(order.id)}
                          >
                            <CancelOutlinedIcon />
                          </IconButton>
                        )}
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {orders.length === 0 && (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No orders found
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Order Details Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          Order Details - #{selectedOrder?.id.slice(-6).toUpperCase()}
        </DialogTitle>
        <DialogContent>
          {selectedOrder && (
            <Stack spacing={2}>
              <Stack direction="row" justifyContent="space-between">
                <Typography variant="body2" color="text.secondary">
                  Order Date:
                </Typography>
                <Typography variant="body2">
                  {selectedOrder.orderDate.toLocaleString()}
                </Typography>
              </Stack>
              
              <Stack direction="row" justifyContent="space-between">
                <Typography variant="body2" color="text.secondary">
                  Status:
                </Typography>
                <Chip
                  label={getStatusLabel(selectedOrder.status)}
                  color={getStatusColor(selectedOrder.status) as any}
                  size="small"
                />
              </Stack>

              <Stack direction="row" justifyContent="space-between">
                <Typography variant="body2" color="text.secondary">
                  Total Amount:
                </Typography>
                <Typography variant="h6" color="primary">
                  ₹{selectedOrder.total}
                </Typography>
              </Stack>

              <Typography variant="subtitle2" sx={{ mt: 2 }}>
                Order Items:
              </Typography>
              {selectedOrder.items.map((item, index) => (
                <Stack key={index} direction="row" justifyContent="space-between">
                  <Typography variant="body2">
                    Meal ID: {item.mealId} (x{item.quantity})
                  </Typography>
                  <Typography variant="body2">
                    Size: {item.size.charAt(0).toUpperCase() + item.size.slice(1)}
                  </Typography>
                </Stack>
              ))}

              {selectedOrder.specialInstructions && (
                <>
                  <Typography variant="subtitle2">
                    Special Instructions:
                  </Typography>
                  <Typography variant="body2">
                    {selectedOrder.specialInstructions}
                  </Typography>
                </>
              )}
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default OrderManagement;