import { MealType, MealCategory, OrderStatus, MealSize, DayOfWeek } from '../types/enums';

// Mock data for global state store
export const mockStore = {
  user: {
    id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'student' as const,
    hostelBlock: 'A-Block',
    roomNumber: '201'
  },
  cart: {
    items: [
      {
        id: 'meal-1',
        name: 'Paneer Butter Masala',
        type: MealType.LUNCH,
        size: MealSize.MEDIUM,
        quantity: 2,
        price: 120
      }
    ],
    total: 240
  }
};

// Mock data for API queries
export const mockQuery = {
  meals: [
    {
      id: 'meal-1',
      name: 'Paneer Butter Masala',
      description: 'Rich and creamy paneer curry with butter and spices',
      type: MealType.LUNCH,
      category: MealCategory.VEGETARIAN,
      price: 120,
      image: 'https://images.unsplash.com/photo-1697880691504-a13456a05e3f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwzfHxwYW5lZXIlMjBjdXJyeSUyMGluZGlhbiUyMGZvb2QlMjBjcmVhbXklMjBzYXVjZXxlbnwwfDJ8fG9yYW5nZXwxNzU2ODM4MzEzfDA&ixlib=rb-4.1.0&q=85',
      rating: 4.5,
      reviewCount: 45,
      isAvailable: true,
      preparationTime: 20,
      calories: 350
    },
    {
      id: 'meal-2',
      name: 'Masala Dosa',
      description: 'Crispy South Indian crepe with spiced potato filling',
      type: MealType.BREAKFAST,
      category: MealCategory.VEGETARIAN,
      price: 80,
      image: 'https://images.unsplash.com/photo-1630383249896-424e482df921?w=400',
      rating: 4.3,
      reviewCount: 32,
      isAvailable: true,
      preparationTime: 15,
      calories: 280
    },
    {
      id: 'meal-3',
      name: 'Chicken Biryani',
      description: 'Aromatic basmati rice with tender chicken pieces',
      type: MealType.DINNER,
      category: MealCategory.NON_VEGETARIAN,
      price: 180,
      image: 'https://images.unsplash.com/photo-1697880691504-a13456a05e3f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw2fHxiaXJ5YW5pJTIwY2hpY2tlbiUyMHJpY2UlMjBhcm9tYXRpY3xlbnwwfDJ8fG9yYW5nZXwxNzU2ODM4MzEzfDA&ixlib=rb-4.1.0&q=85',
      rating: 4.7,
      reviewCount: 68,
      isAvailable: true,
      preparationTime: 30,
      calories: 450
    }
  ],
  orders: [
    {
      id: 'order-1',
      userId: 'user-1',
      items: [
        {
          mealId: 'meal-1',
          quantity: 2,
          size: MealSize.MEDIUM
        }
      ],
      status: OrderStatus.CONFIRMED,
      total: 240,
      orderDate: new Date('2024-01-15T10:30:00'),
      deliveryTime: new Date('2024-01-15T12:00:00')
    }
  ],
  weeklyMenu: [
    {
      day: DayOfWeek.MONDAY,
      meals: {
        breakfast: ['meal-2'],
        lunch: ['meal-1'],
        dinner: ['meal-3']
      }
    }
  ],
  reviews: [
    {
      id: 'review-1',
      userId: 'user-1',
      mealId: 'meal-1',
      rating: 5,
      comment: 'Absolutely delicious! The paneer was perfectly cooked.',
      date: new Date('2024-01-14T18:00:00')
    }
  ]
};

// Mock data for root component props
export const mockRootProps = {
  currentUser: {
    id: 'user-1',
    name: 'John Doe',
    avatar: 'https://i.pravatar.cc/150?img=1'
  },
  notifications: [
    {
      id: 'notif-1',
      message: 'Your lunch order is ready for pickup',
      timestamp: new Date('2024-01-15T12:00:00'),
      isRead: false
    }
  ],
  dashboardStats: {
    todayOrders: 15,
    weeklyOrders: 89,
    totalMeals: 156,
    avgRating: 4.4
  }
};