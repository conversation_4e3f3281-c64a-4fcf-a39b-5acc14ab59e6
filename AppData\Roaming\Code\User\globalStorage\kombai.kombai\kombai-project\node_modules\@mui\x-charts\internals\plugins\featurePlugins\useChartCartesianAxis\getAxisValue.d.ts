import { ComputedAxis } from "../../../../models/axis.js";
/**
 * For a pointer coordinate, this function returns the dataIndex associated.
 * Returns `-1` if no dataIndex matches.
 */
export declare function getAxisIndex(axisConfig: ComputedAxis, pointerValue: number): number;
/**
 * For a pointer coordinate, this function returns the value associated.
 * Returns `null` if the coordinate has no value associated.
 */
export declare function getAxisValue(axisConfig: ComputedAxis, pointerValue: number, dataIndex: number | null): number | Date | null;