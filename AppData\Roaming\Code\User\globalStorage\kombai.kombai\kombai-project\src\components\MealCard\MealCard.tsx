import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardMedia,
  Typography,
  Button,
  Stack,
  Chip,
  Rating,
  IconButton,
  Box,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import FavoriteOutlinedIcon from '@mui/icons-material/FavoriteOutlined';
import FavoriteBorderOutlinedIcon from '@mui/icons-material/FavoriteBorderOutlined';
import AccessTimeOutlinedIcon from '@mui/icons-material/AccessTimeOutlined';
import LocalFireDepartmentOutlinedIcon from '@mui/icons-material/LocalFireDepartmentOutlined';
import { MealItem } from '../../types/interfaces';
import { MealSize } from '../../types/enums';

interface MealCardProps {
  meal: MealItem;
  onAddToCart: (meal: MealItem, size: MealSize, quantity: number) => void;
  onViewDetails: (meal: MealItem) => void;
}

const MealCard: React.FC<MealCardProps> = ({ meal, onAddToCart, onViewDetails }) => {
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedSize, setSelectedSize] = useState<MealSize>(MealSize.MEDIUM);
  const [quantity, setQuantity] = useState(1);

  const handleFavoriteToggle = () => {
    setIsFavorite(!isFavorite);
  };

  const handleAddToCart = () => {
    onAddToCart(meal, selectedSize, quantity);
  };

  const getSizeMultiplier = (size: MealSize): number => {
    switch (size) {
      case MealSize.SMALL: return 0.8;
      case MealSize.MEDIUM: return 1;
      case MealSize.LARGE: return 1.3;
      case MealSize.EXTRA_LARGE: return 1.6;
      default: return 1;
    }
  };

  const calculatePrice = () => {
    return Math.round(meal.price * getSizeMultiplier(selectedSize) * quantity);
  };

  const getCategoryColor = () => {
    switch (meal.category) {
      case 'vegetarian': return 'success';
      case 'non_vegetarian': return 'error';
      case 'vegan': return 'info';
      case 'gluten_free': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Card sx={{ maxWidth: 345, height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ position: 'relative' }}>
        <CardMedia
          component="img"
          height="200"
          image={meal.image}
          alt={`${meal.name} - Karyna Panchenko on Unsplash`}
          sx={{ objectFit: 'cover' }}
        />
        <IconButton
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 1)',
            },
          }}
          onClick={handleFavoriteToggle}
        >
          {isFavorite ? (
            <FavoriteOutlinedIcon color="error" />
          ) : (
            <FavoriteBorderOutlinedIcon />
          )}
        </IconButton>
        
        <Chip
          label={meal.category.replace('_', ' ').toUpperCase()}
          color={getCategoryColor() as any}
          size="small"
          sx={{
            position: 'absolute',
            top: 8,
            left: 8,
            fontWeight: 600,
          }}
        />
      </Box>

      <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        <Typography gutterBottom variant="h6" component="div" sx={{ fontWeight: 600 }}>
          {meal.name}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flexGrow: 1 }}>
          {meal.description}
        </Typography>

        <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
          <Stack direction="row" alignItems="center" spacing={0.5}>
            <Rating value={meal.rating} precision={0.1} size="small" readOnly />
            <Typography variant="body2" color="text.secondary">
              ({meal.reviewCount})
            </Typography>
          </Stack>
        </Stack>

        <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
          <Stack direction="row" alignItems="center" spacing={0.5}>
            <AccessTimeOutlinedIcon fontSize="small" color="action" />
            <Typography variant="caption" color="text.secondary">
              {meal.preparationTime}min
            </Typography>
          </Stack>
          <Stack direction="row" alignItems="center" spacing={0.5}>
            <LocalFireDepartmentOutlinedIcon fontSize="small" color="action" />
            <Typography variant="caption" color="text.secondary">
              {meal.calories} cal
            </Typography>
          </Stack>
        </Stack>

        <Stack spacing={2}>
          <Stack direction="row" spacing={1}>
            <FormControl size="small" sx={{ minWidth: 80 }}>
              <InputLabel>Size</InputLabel>
              <Select
                value={selectedSize}
                label="Size"
                onChange={(e) => setSelectedSize(e.target.value as MealSize)}
              >
                <MenuItem value={MealSize.SMALL}>Small</MenuItem>
                <MenuItem value={MealSize.MEDIUM}>Medium</MenuItem>
                <MenuItem value={MealSize.LARGE}>Large</MenuItem>
                <MenuItem value={MealSize.EXTRA_LARGE}>XL</MenuItem>
              </Select>
            </FormControl>
            
            <FormControl size="small" sx={{ minWidth: 60 }}>
              <InputLabel>Qty</InputLabel>
              <Select
                value={quantity}
                label="Qty"
                onChange={(e) => setQuantity(e.target.value as number)}
              >
                {[1, 2, 3, 4, 5].map((num) => (
                  <MenuItem key={num} value={num}>{num}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Stack>

          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="h6" color="primary" sx={{ fontWeight: 700 }}>
              ₹{calculatePrice()}
            </Typography>
            <Stack direction="row" spacing={1}>
              <Button
                variant="outlined"
                size="small"
                onClick={() => onViewDetails(meal)}
              >
                Details
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={handleAddToCart}
                disabled={!meal.isAvailable}
              >
                {meal.isAvailable ? 'Add to Cart' : 'Unavailable'}
              </Button>
            </Stack>
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default MealCard;