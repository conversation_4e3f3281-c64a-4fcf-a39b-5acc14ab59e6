import { ChartsLocaleText } from "./chartsLocaleTextApi.js";
/**
 * Helper to pass translation to all charts thanks to the MUI theme.
 * @param chartsTranslations The translation object.
 * @returns an object to pass the translation by using the MUI theme default props
 */
export declare const getChartsLocalization: (chartsTranslations: Partial<ChartsLocaleText>) => {
  components: {
    MuiChartsLocalizationProvider: {
      defaultProps: {
        localeText: {
          loading?: string | undefined;
          noData?: string | undefined;
          zoomIn?: string | undefined;
          zoomOut?: string | undefined;
          toolbarExport?: string | undefined;
          toolbarExportPrint?: string | undefined;
          toolbarExportImage?: ((mimeType: import("./imageMimeTypes.js").ChartImageExportMimeType | (string & {})) => string) | undefined;
        };
      };
    };
  };
};